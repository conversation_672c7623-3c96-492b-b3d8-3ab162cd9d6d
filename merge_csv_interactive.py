import os
import csv
import datetime
import re

def parse_filename_datetime(filename):
    """
    Parsuje datę i czas z nazwy pliku w formacie -24juin2025-113136.csv
    Zwraca datetime object lub None jeśli nie można sparsować
    """
    # Słownik francuskich nazw miesięcy
    french_months = {
        'janvier': 1, 'février': 2, 'mars': 3, 'avril': 4, 'mai': 5, 'juin': 6,
        'juillet': 7, 'août': 8, 'septembre': 9, 'octobre': 10, 'novembre': 11, 'décembre': 12,
        'janv': 1, 'févr': 2, 'mars': 3, 'avr': 4, 'mai': 5, 'juin': 6,
        'juil': 7, 'août': 8, 'sept': 9, 'oct': 10, 'nov': 11, 'déc': 12
    }

    # Wzorzec dla formatu -24juin2025-113136.csv lub -24Jun2025-113136.csv
    pattern = r'-(\d{1,2})([a-zA-Zàâäéèêëï<PERSON>ôö<PERSON>]+)(\d{4})-(\d{2})(\d{2})(\d{2})\.csv'
    match = re.search(pattern, filename)

    if match:
        day = int(match.group(1))
        month_name = match.group(2).lower()
        year = int(match.group(3))
        hour = int(match.group(4))
        minute = int(match.group(5))
        second = int(match.group(6))

        # Znajdź numer miesiąca (obsługa zarówno francuskich jak i angielskich nazw)
        month = french_months.get(month_name)
        if not month:
            # Spróbuj angielskie nazwy miesięcy
            english_months = {
                'january': 1, 'february': 2, 'march': 3, 'april': 4, 'may': 5, 'june': 6,
                'july': 7, 'august': 8, 'september': 9, 'october': 10, 'november': 11, 'december': 12,
                'jan': 1, 'feb': 2, 'mar': 3, 'apr': 4, 'may': 5, 'jun': 6,
                'jul': 7, 'aug': 8, 'sep': 9, 'oct': 10, 'nov': 11, 'dec': 12
            }
            month = english_months.get(month_name)

        if month:
            try:
                return datetime.datetime(year, month, day, hour, minute, second)
            except ValueError:
                pass

    return None

def sort_files_by_datetime(files):
    """
    Sortuje pliki według daty i czasu w nazwie pliku (starsze pierwsze)
    """
    def get_sort_key(filename):
        dt = parse_filename_datetime(filename)
        if dt:
            return dt
        else:
            # Jeśli nie można sparsować daty, użyj nazwy pliku jako klucz sortowania
            return datetime.datetime.min

    return sorted(files, key=get_sort_key)

def parse_filename_datetime(filename):
    """
    Parsuje datę i czas z nazwy pliku w formacie -24juin2025-113136.csv
    Zwraca datetime object lub None jeśli nie można sparsować
    """
    # Słownik francuskich nazw miesięcy
    french_months = {
        'janvier': 1, 'février': 2, 'mars': 3, 'avril': 4, 'mai': 5, 'juin': 6,
        'juillet': 7, 'août': 8, 'septembre': 9, 'octobre': 10, 'novembre': 11, 'décembre': 12,
        'janv': 1, 'févr': 2, 'mars': 3, 'avr': 4, 'mai': 5, 'juin': 6,
        'juil': 7, 'août': 8, 'sept': 9, 'oct': 10, 'nov': 11, 'déc': 12
    }

    # Wzorzec dla formatu -24juin2025-113136.csv
    pattern = r'-(\d{1,2})([a-zA-Zàâäéèêëïîôöùûüÿç]+)(\d{4})-(\d{2})(\d{2})(\d{2})\.csv'
    match = re.search(pattern, filename)

    if match:
        day = int(match.group(1))
        month_name = match.group(2).lower()
        year = int(match.group(3))
        hour = int(match.group(4))
        minute = int(match.group(5))
        second = int(match.group(6))

        # Znajdź numer miesiąca
        month = french_months.get(month_name)
        if month:
            try:
                return datetime.datetime(year, month, day, hour, minute, second)
            except ValueError:
                pass

    return None

def sort_files_by_datetime(files):
    """
    Sortuje pliki według daty i czasu w nazwie pliku (starsze pierwsze)
    """
    def get_sort_key(filepath):
        filename = os.path.basename(filepath)
        dt = parse_filename_datetime(filename)
        if dt:
            return dt
        else:
            # Jeśli nie można sparsować daty, użyj czasu modyfikacji pliku
            try:
                return datetime.datetime.fromtimestamp(os.path.getmtime(filepath))
            except:
                return datetime.datetime.min

    return sorted(files, key=get_sort_key)

def list_csv_files(base_dirs):
    csv_files = []
    for base in base_dirs:
        files_in_dir = []
        for fname in os.listdir(base):
            if fname.endswith('.csv'):
                files_in_dir.append(fname)
        # Sortuj pliki w każdym katalogu chronologicznie
        files_in_dir = sort_files_by_datetime(files_in_dir)
        # Dodaj pełne ścieżki do listy
        for fname in files_in_dir:
            csv_files.append(os.path.join(base, fname))
    return csv_files

def choose_files_interactively(files):
    print("\n" + "="*60)
    print("           DOSTĘPNE PLIKI CSV")
    print("="*60)

    # Grupuj pliki według folderów
    grouped_files = {}
    for idx, f in enumerate(files):
        folder = os.path.dirname(f)
        if folder not in grouped_files:
            grouped_files[folder] = []
        grouped_files[folder].append((idx, f))

    # Wyświetl pliki pogrupowane
    for folder, file_list in grouped_files.items():
        folder_name = folder if folder else 'GŁÓWNY'
        print(f"\n┌─ FOLDER: {folder_name} ─{'─' * (50 - len(folder_name))}")
        for idx, f in file_list:
            print(f"│  {idx+1:2d}: {os.path.basename(f)}")
        print("└" + "─" * 58)

    print("\nPodaj numery plików do połączenia, oddzielone przecinkami,")
    print("w wybranej kolejności (np. 2,1):")
    order = input("Twój wybór: ").strip()
    indices = [int(i)-1 for i in order.split(',') if i.strip().isdigit()]
    chosen = [files[i] for i in indices if 0 <= i < len(files)]
    return chosen

def get_country_code(filename):
    # Zakładamy, że kod kraju to pierwsze 2 litery nazwy pliku
    base = os.path.basename(filename)
    return base[:2].upper()

def merge_csv(files, output_file):
    if not files:
        print("Nie wybrano plików.")
        return
    with open(output_file, 'w', newline='', encoding='utf-8') as fout:
        writer = None
        for idx, fname in enumerate(files):
            with open(fname, 'r', encoding='utf-8') as fin:
                reader = csv.reader(fin)
                rows = list(reader)
                # Usuń pierwszy wiersz (nagłówek generalny) ze wszystkich plików
                rows = rows[1:]
                if idx == 0:
                    # Z pierwszego pliku: zachowaj nagłówek danych (drugi wiersz) i resztę
                    writer = csv.writer(fout)
                    writer.writerows(rows)
                else:
                    # Z kolejnych plików: pomiń nagłówek danych (pierwszy wiersz po usunięciu nagłówka generalnego)
                    writer.writerows(rows[1:])
    print(f'Plik wynikowy zapisany jako: {output_file}')

def fix_csv_quotes(input_file, output_file):
    import csv

    processed_count = 0
    error_count = 0

    with open(input_file, 'r', encoding='utf-8') as fin, \
         open(output_file, 'w', newline='', encoding='utf-8') as fout:

        # Kolumny bez cudzysłowów: 6, 7, 8, 9, 10, 11 (indeksy 5, 6, 7, 8, 9, 10)
        no_quotes_columns = {5, 6, 7, 8, 9, 10}

        # Użyj csv.reader dla całego pliku - lepiej radzi sobie z wieloliniowymi polami
        reader = csv.reader(fin)

        for row_num, row in enumerate(reader, 1):
            try:
                # Sprawdź czy wiersz ma oczekiwaną liczbę kolumn (powinno być co najmniej 13)
                if len(row) < 13:
                    print(f"OSTRZEŻENIE: Wiersz {row_num} ma tylko {len(row)} kolumn, oczekiwano co najmniej 13")
                    if len(row) > 0:
                        print(f"Pierwsza kolumna: {row[0][:50]}...")
                    error_count += 1
                    # Spróbuj naprawić wiersz dodając brakujące kolumny
                    while len(row) < 13:
                        row.append("")

                # Napraw kolumny - niektóre z cudzysłowami, niektóre bez
                fixed_row = []
                for idx, field in enumerate(row):
                    if idx in no_quotes_columns:  # Kolumny 6-11 - pozostaw bez cudzysłowów
                        # Usuń cudzysłowy jeśli są i oczyść pole
                        cleaned_field = str(field).strip('"').replace('\n', ' ').replace('\r', ' ')
                        fixed_row.append(cleaned_field)
                    else:  # Wszystkie inne kolumny - otocz cudzysłowami
                        # Usuń istniejące cudzysłowy na początku i końcu, jeśli są
                        cleaned_field = str(field).strip('"').replace('\n', ' ').replace('\r', ' ')
                        # Escape wewnętrzne cudzysłowy
                        escaped_field = cleaned_field.replace('"', '""')
                        # Otocz cudzysłowami
                        fixed_row.append(f'"{escaped_field}"')

                # Zapisz wiersz
                fout.write(','.join(fixed_row) + '\n')
                processed_count += 1

            except Exception as e:
                print(f"BŁĄD w wierszu {row_num}: {str(e)}")
                if len(row) > 0:
                    print(f"Pierwsza kolumna: {row[0][:50]}...")
                error_count += 1
                # W przypadku błędu, spróbuj zapisać wiersz z podstawowymi polami
                try:
                    basic_row = [str(field).replace('\n', ' ').replace('\r', ' ') for field in row]
                    while len(basic_row) < 13:
                        basic_row.append("")
                    fout.write(','.join([f'"{field}"' if idx not in no_quotes_columns else field
                                       for idx, field in enumerate(basic_row)]) + '\n')
                except:
                    print(f"Nie można zapisać wiersza {row_num}")
                    continue

    print(f'Plik naprawiony zapisany jako: {output_file}')
    print(f'Przetworzono {processed_count} wierszy, błędów: {error_count}')


def list_all_csv_files_grouped(base_path, subdirs):
    grouped = {}
    for sub in subdirs:
        dir_path = os.path.join(base_path, sub)
        if not os.path.isdir(dir_path):
            continue
        files = [f for f in os.listdir(dir_path) if f.endswith('.csv')]
        # Sortuj pliki chronologicznie (starsze pierwsze)
        files = sort_files_by_datetime(files)
        grouped[sub] = [os.path.join(sub, f) for f in files]
    # Pliki w katalogu głównym
    main_files = [f for f in os.listdir(base_path) if f.endswith('.csv')]
    if main_files:
        # Sortuj również pliki główne
        main_files = sort_files_by_datetime(main_files)
        grouped['GŁÓWNY'] = main_files
    return grouped

def write_csv_with_formatting(output_file, rows):
    """
    Zapisuje wiersze CSV z poprawnym formatowaniem:
    - Kolumny 6-11 (indeksy 5-10) bez cudzysłowów
    - Wszystkie inne kolumny z cudzysłowami
    """
    with open(output_file, 'w', encoding='utf-8') as fout:
        # Kolumny bez cudzysłowów: 6, 7, 8, 9, 10, 11 (indeksy 5, 6, 7, 8, 9, 10)
        no_quotes_columns = {5, 6, 7, 8, 9, 10}

        for row_num, row in enumerate(rows, 1):
            try:
                # Sprawdź czy wiersz ma wystarczającą liczbę kolumn
                if len(row) < 13:
                    print(f"OSTRZEŻENIE: Wiersz {row_num} ma tylko {len(row)} kolumn")

                # Formatuj każdy wiersz
                fixed_row = []
                for idx, field in enumerate(row):
                    if idx in no_quotes_columns:  # Kolumny 6-11 - pozostaw bez cudzysłowów
                        # Usuń cudzysłowy jeśli są
                        cleaned_field = str(field).strip('"')
                        fixed_row.append(cleaned_field)
                    else:  # Wszystkie inne kolumny - otocz cudzysłowami
                        # Usuń istniejące cudzysłowy na początku i końcu, jeśli są
                        cleaned_field = str(field).strip('"')
                        # Escape wewnętrzne cudzysłowy
                        escaped_field = cleaned_field.replace('"', '""')
                        # Otocz cudzysłowami
                        fixed_row.append(f'"{escaped_field}"')

                # Zapisz wiersz
                fout.write(','.join(fixed_row) + '\n')

            except Exception as e:
                print(f"BŁĄD przy zapisywaniu wiersza {row_num}: {str(e)}")
                # W przypadku błędu, spróbuj zapisać oryginalny wiersz
                try:
                    fout.write(','.join([str(field) for field in row]) + '\n')
                except:
                    print(f"Nie można zapisać wiersza {row_num}")
                    continue

def filter_csv_by_date(input_file, output_file, excluded_file, cutoff_date):
    import csv

    # Najpierw przeczytaj wszystkie dane
    with open(input_file, 'r', encoding='utf-8') as fin:
        reader = csv.reader(fin)
        rows = list(reader)

    if len(rows) < 2:
        print("Plik nie zawiera nagłówków i danych.")
        return

    date_col_idx = 5
    try:
        cutoff_dt = datetime.datetime.strptime(cutoff_date, "%Y-%m-%d")
    except Exception as e:
        print("Błędny format daty. Użyj YYYY-MM-DD.")
        return

    matched_count = 0
    excluded_count = 0
    error_count = 0

    # Listy do przechowania wierszy
    remaining_rows = [rows[0], rows[1]]  # nagłówki
    excluded_rows = [rows[0], rows[1]]   # nagłówki

    print(f"Przetwarzanie pliku: {input_file}")
    print(f"Szukam wierszy z datą: {cutoff_date}")
    print(f"Liczba wierszy danych do przetworzenia: {len(rows) - 2}")

    for row_idx, row in enumerate(rows[2:], start=3):
        try:
            if len(row) <= date_col_idx:
                remaining_rows.append(row)
                error_count += 1
                continue

            val = row[date_col_idx].strip('"').strip()

            # Obsługa formatu "YYYY-MM-DD HH:MM:SS"
            row_dt = datetime.datetime.strptime(val, "%Y-%m-%d %H:%M:%S")

            # Sprawdź czy data wiersza to dokładnie ta sama data (ignorując czas)
            if row_dt.date() == cutoff_dt.date():
                excluded_rows.append(row)  # Wiersze z podaną datą idą do pliku excluded (wycięte)
                matched_count += 1
            else:
                remaining_rows.append(row)  # Wiersze z innymi datami zostają w oryginalnym pliku
                excluded_count += 1
        except Exception as e:
            # W przypadku błędu parsowania daty, wiersz zostaje w oryginalnym pliku
            remaining_rows.append(row)
            error_count += 1

    # Teraz zapisz pliki z poprawnym formatowaniem
    write_csv_with_formatting(output_file, remaining_rows)
    write_csv_with_formatting(excluded_file, excluded_rows)

    print(f'\n=== PODSUMOWANIE ===')
    print(f'Wycięto {matched_count} wierszy z datą {cutoff_date} do pliku: {excluded_file}')
    print(f'Pozostało {excluded_count} wierszy z innymi datami w oryginalnym pliku: {output_file}')
    print(f'Błędów parsowania: {error_count}')

def menu():
    while True:
        print("\n" + "="*60)
        print("              MENU GŁÓWNE")
        print("="*60)
        print("1. Połącz pliki CSV")
        print("2. Napraw plik CSV (wszystkie stringi w cudzysłowie)")
        print("3. Wytnij wiersze z podaną datą do oddzielnego pliku")
        print("4. Zakończ")
        print("─" * 60)
        choice = input("Twój wybór: ").strip()
        if choice == '1':
            base_dirs = ['FR', 'SE', 'UK']
            csv_files = list_csv_files(base_dirs)
            if not csv_files:
                print("Brak plików CSV w katalogach FR, SE, UK.")
                continue
            chosen_files = choose_files_interactively(csv_files)
            if not chosen_files:
                print("Nie wybrano plików.")
                continue
            country_code = get_country_code(chosen_files[0])
            output_file = f'Amazon_Earnings_{country_code}.csv'
            merge_csv(chosen_files, output_file)
        elif choice == '2':
            base_path = os.path.dirname(os.path.abspath(__file__))
            subdirs = ['FR', 'SE', 'UK']
            grouped = list_all_csv_files_grouped(base_path, subdirs)
            print("\n" + "="*60)
            print("           DOSTĘPNE PLIKI CSV")
            print("="*60)
            file_list = []
            for group, files in grouped.items():
                print(f"\n┌─ FOLDER: {group} ─{'─' * (50 - len(group))}")
                if not files:
                    print("│  (brak plików)")
                else:
                    for idx, f in enumerate(files):
                        print(f"│  {len(file_list)+1:2d}: {f}")
                        file_list.append(os.path.join(base_path, f) if group != 'GŁÓWNY' else os.path.join(base_path, f))
                print("└" + "─" * 58)
            if not file_list:
                print("Brak plików CSV do naprawy.")
                continue
            num = input("Podaj numer pliku do naprawy: ").strip()
            if not num.isdigit() or int(num) < 1 or int(num) > len(file_list):
                print("Nieprawidłowy wybór.")
                continue
            path = file_list[int(num)-1]
            output_file = os.path.splitext(path)[0] + '_fix.csv'
            fix_csv_quotes(path, output_file)
        elif choice == '3':
            base_path = os.path.dirname(os.path.abspath(__file__))
            subdirs = ['FR', 'SE', 'UK']
            grouped = list_all_csv_files_grouped(base_path, subdirs)
            print("\n" + "="*60)
            print("           DOSTĘPNE PLIKI CSV")
            print("="*60)
            file_list = []
            for group, files in grouped.items():
                print(f"\n┌─ FOLDER: {group} ─{'─' * (50 - len(group))}")
                if not files:
                    print("│  (brak plików)")
                else:
                    for idx, f in enumerate(files):
                        print(f"│  {len(file_list)+1:2d}: {f}")
                        file_list.append(os.path.join(base_path, f) if group != 'GŁÓWNY' else os.path.join(base_path, f))
                print("└" + "─" * 58)
            if not file_list:
                print("Brak plików CSV do filtrowania.")
                continue
            num = input("Podaj numer pliku do filtrowania: ").strip()
            if not num.isdigit() or int(num) < 1 or int(num) > len(file_list):
                print("Nieprawidłowy wybór.")
                continue
            path = file_list[int(num)-1]
            output_file = path  # nadpisujemy oryginał
            excluded_file = os.path.splitext(path)[0] + '_dataexcluded.csv'
            cutoff_date = input("Podaj datę graniczną (YYYY-MM-DD): ").strip()
            filter_csv_by_date(path, output_file, excluded_file, cutoff_date)
        elif choice == '4':
            print("Zakończono działanie skryptu.")
            break
        else:
            print("Nieznana opcja.")

if __name__ == "__main__":
    menu()
