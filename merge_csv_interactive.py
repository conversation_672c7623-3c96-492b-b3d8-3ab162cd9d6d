import os
import csv
import datetime

def list_csv_files(base_dirs):
    csv_files = []
    for base in base_dirs:
        for fname in os.listdir(base):
            if fname.endswith('.csv'):
                csv_files.append(os.path.join(base, fname))
    return csv_files

def choose_files_interactively(files):
    print("Dostępne pliki CSV:")
    for idx, f in enumerate(files):
        print(f"{idx+1}: {f}")
    print("Podaj numery plików do połączenia, oddzie<PERSON> przecinkami, w wybranej kolejności (np. 2,1):")
    order = input().strip()
    indices = [int(i)-1 for i in order.split(',') if i.strip().isdigit()]
    chosen = [files[i] for i in indices if 0 <= i < len(files)]
    return chosen

def get_country_code(filename):
    # Zakładamy, że kod kraju to pierwsze 2 litery nazwy pliku
    base = os.path.basename(filename)
    return base[:2].upper()

def merge_csv(files, output_file):
    if not files:
        print("Nie wybrano plików.")
        return
    with open(output_file, 'w', newline='', encoding='utf-8') as fout:
        writer = None
        for idx, fname in enumerate(files):
            with open(fname, 'r', encoding='utf-8') as fin:
                reader = csv.reader(fin)
                rows = list(reader)
                # Usuń pierwszy wiersz (nagłówek generalny) ze wszystkich plików
                rows = rows[1:]
                if idx == 0:
                    # Z pierwszego pliku: zachowaj nagłówek danych (drugi wiersz) i resztę
                    writer = csv.writer(fout)
                    writer.writerows(rows)
                else:
                    # Z kolejnych plików: pomiń nagłówek danych (pierwszy wiersz po usunięciu nagłówka generalnego)
                    writer.writerows(rows[1:])
    print(f'Plik wynikowy zapisany jako: {output_file}')

def fix_csv_quotes(input_file, output_file):
    import csv
    with open(input_file, 'r', encoding='utf-8') as fin, \
         open(output_file, 'w', newline='', encoding='utf-8') as fout:
        reader = csv.reader(fin)
        writer = csv.writer(fout, quoting=csv.QUOTE_ALL, escapechar='\\')
        for row in reader:
            # Wszystkie pola będą otoczone cudzysłowami, a cudzysłowy/apostrofy w środku będą poprawnie obsłużone
            writer.writerow(row)
    print(f'Plik naprawiony zapisany jako: {output_file}')


def list_all_csv_files_grouped(base_path, subdirs):
    grouped = {}
    for sub in subdirs:
        dir_path = os.path.join(base_path, sub)
        if not os.path.isdir(dir_path):
            continue
        files = [f for f in os.listdir(dir_path) if f.endswith('.csv')]
        grouped[sub] = [os.path.join(sub, f) for f in files]
    # Pliki w katalogu głównym
    main_files = [f for f in os.listdir(base_path) if f.endswith('.csv')]
    if main_files:
        grouped['GŁÓWNY'] = main_files
    return grouped

def filter_csv_by_date(input_file, output_file, excluded_file, cutoff_date):
    import csv
    with open(input_file, 'r', encoding='utf-8') as fin, \
         open(output_file, 'w', newline='', encoding='utf-8') as fout, \
         open(excluded_file, 'w', newline='', encoding='utf-8') as fex:
        reader = csv.reader(fin)
        writer = csv.writer(fout)
        ex_writer = csv.writer(fex)
        rows = list(reader)
        if len(rows) < 2:
            print("Plik nie zawiera nagłówków i danych.")
            return
        writer.writerow(rows[0])
        writer.writerow(rows[1])
        ex_writer.writerow(rows[0])
        ex_writer.writerow(rows[1])
        date_col_idx = 5
        try:
            cutoff_dt = datetime.datetime.strptime(cutoff_date, "%Y-%m-%d")
        except Exception as e:
            print("Błędny format daty. Użyj YYYY-MM-DD.")
            return

        matched_count = 0
        excluded_count = 0

        for row in rows[2:]:
            try:
                val = row[date_col_idx].strip('"')
                # Obsługa formatu "YYYY-MM-DD HH:MM:SS"
                row_dt = datetime.datetime.strptime(val, "%Y-%m-%d %H:%M:%S")
                # Sprawdź czy data wiersza to dokładnie ta sama data (ignorując czas)
                if row_dt.date() == cutoff_dt.date():
                    ex_writer.writerow(row)  # Wiersze z podaną datą idą do pliku excluded (wycięte)
                    matched_count += 1
                else:
                    writer.writerow(row)  # Wiersze z innymi datami zostają w oryginalnym pliku
                    excluded_count += 1
            except Exception:
                # W przypadku błędu parsowania daty, wiersz zostaje w oryginalnym pliku
                writer.writerow(row)
                excluded_count += 1

        print(f'Wycięto {matched_count} wierszy z datą {cutoff_date} do pliku: {excluded_file}')
        print(f'Pozostało {excluded_count} wierszy z innymi datami w oryginalnym pliku: {output_file}')

def menu():
    while True:
        print("Wybierz opcję:")
        print("1. Połącz pliki CSV")
        print("2. Napraw plik CSV (wszystkie stringi w cudzysłowie)")
        print("3. Wytnij wiersze z podaną datą do oddzielnego pliku")
        print("4. Zakończ")
        choice = input("Twój wybór: ").strip()
        if choice == '1':
            base_dirs = ['FR', 'SE', 'UK']
            csv_files = list_csv_files(base_dirs)
            if not csv_files:
                print("Brak plików CSV w katalogach FR, SE, UK.")
                return
            chosen_files = choose_files_interactively(csv_files)
            if not chosen_files:
                print("Nie wybrano plików.")
                return
            country_code = get_country_code(chosen_files[0])
            output_file = f'Amazon_Earnings_{country_code}.csv'
            merge_csv(chosen_files, output_file)
        elif choice == '2':
            base_path = os.path.dirname(os.path.abspath(__file__))
            subdirs = ['FR', 'SE', 'UK']
            grouped = list_all_csv_files_grouped(base_path, subdirs)
            print("Dostępne pliki CSV:")
            file_list = []
            for group, files in grouped.items():
                print(f"[{group}]")
                for idx, f in enumerate(files):
                    print(f"  {len(file_list)+1}: {f}")
                    file_list.append(os.path.join(base_path, f) if group != 'GŁÓWNY' else os.path.join(base_path, f))
            if not file_list:
                print("Brak plików CSV do naprawy.")
                return
            num = input("Podaj numer pliku do naprawy: ").strip()
            if not num.isdigit() or int(num) < 1 or int(num) > len(file_list):
                print("Nieprawidłowy wybór.")
                return
            path = file_list[int(num)-1]
            output_file = os.path.splitext(path)[0] + '_fix.csv'
            fix_csv_quotes(path, output_file)
        elif choice == '3':
            base_path = os.path.dirname(os.path.abspath(__file__))
            subdirs = ['FR', 'SE', 'UK']
            grouped = list_all_csv_files_grouped(base_path, subdirs)
            print("Dostępne pliki CSV:")
            file_list = []
            for group, files in grouped.items():
                print(f"[{group}]")
                for idx, f in enumerate(files):
                    print(f"  {len(file_list)+1}: {f}")
                    file_list.append(os.path.join(base_path, f) if group != 'GŁÓWNY' else os.path.join(base_path, f))
            if not file_list:
                print("Brak plików CSV do filtrowania.")
                return
            num = input("Podaj numer pliku do filtrowania: ").strip()
            if not num.isdigit() or int(num) < 1 or int(num) > len(file_list):
                print("Nieprawidłowy wybór.")
                return
            path = file_list[int(num)-1]
            output_file = path  # nadpisujemy oryginał
            excluded_file = os.path.splitext(path)[0] + '_dataexcluded.csv'
            cutoff_date = input("Podaj datę graniczną (YYYY-MM-DD): ").strip()
            filter_csv_by_date(path, output_file, excluded_file, cutoff_date)
        elif choice == '4':
            print("Zakończono działanie skryptu.")
            break
        else:
            print("Nieznana opcja.")

if __name__ == "__main__":
    menu()
